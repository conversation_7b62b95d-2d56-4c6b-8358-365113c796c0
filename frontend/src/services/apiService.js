import axios from "axios";
import config from "@/config";

let mambuUser = localStorage.getItem("mambuUser") ?? import.meta.env.VITE_APP_MAMBU_USER;

// Get token from localStorage
const token = localStorage.getItem('token');
console.log('API Service - Token available:', !!token);

// Create a function to get the appropriate API instance for a subsidiary
const getApiForSubsidiary = (subsidiary) => {
  if (!subsidiary) {
    // Default API instance
    return axios.create({
      baseURL: config["BACKEND_SERVICE"],
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        mambuUser,
        ...(token ? { 'Authorization': `Bearer ${token}` } : {})
      },
    });
  }

  // Check if we have a specific endpoint for this subsidiary
  const subsidiaryEndpoint = config.SUBSIDIARY_ENDPOINTS?.[subsidiary];

  if (subsidiaryEndpoint) {
    console.log(`Using custom endpoint for ${subsidiary}: ${subsidiaryEndpoint}`);

    return axios.create({
      baseURL: subsidiaryEndpoint,
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        mambuUser,
        ...(token ? { 'Authorization': `Bearer ${token}` } : {})
      },
    });
  }

  // Fallback to default API
  return axios.create({
    baseURL: config["BACKEND_SERVICE"],
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
      mambuUser,
      ...(token ? { 'Authorization': `Bearer ${token}` } : {})
    },
  });
};

// Default API instance
const api = axios.create({
  baseURL: config["BACKEND_SERVICE"],
  headers: {
    Accept: "application/json",
    "Content-Type": "application/json",
    mambuUser,
    ...(token ? { 'Authorization': `Bearer ${token}` } : {})
  },
});

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', {
      method: config.method,
      url: config.url,
      baseURL: config.baseURL,
      headers: config.headers,
      hasAuth: !!config.headers.Authorization
    });
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for debugging
api.interceptors.response.use(
  (response) => {
    console.log('API Response Success:', {
      status: response.status,
      url: response.config.url,
      hasData: !!response.data
    });
    return response;
  },
  (error) => {
    console.error('API Response Error:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      data: error.response?.data,
      message: error.message
    });
    return Promise.reject(error);
  }
);

// Helper for dynamic headers
const getSubsidiaryHeader = (subsidiary) => {
  // Get the user's subsidiary from localStorage if available
  const user = JSON.parse(localStorage.getItem('user'));
  const userSubsidiary = user?.sub;

  // Special case for premierkenya and premiergroup - they are the same entity
  let specialCaseHandled = false;
  if (userSubsidiary && subsidiary) {
    if ((userSubsidiary === 'premierkenya' && subsidiary === 'premiergroup') ||
        (userSubsidiary === 'premiergroup' && subsidiary === 'premierkenya')) {
      console.log('Special case: premierkenya and premiergroup are treated as the same entity');
      // Use the requested subsidiary in this special case
      specialCaseHandled = true;
    }
  }

  // IMPORTANT: Always use the user's subsidiary if available
  // This ensures that users can only access their own subsidiary data
  // and prevents cross-subsidiary access issues
  const effectiveSubsidiary = specialCaseHandled ? subsidiary : (userSubsidiary || subsidiary || "platinumkenya");

  // Log the subsidiary being used for debugging
  console.log(`API Request: Using subsidiary ${effectiveSubsidiary} (requested: ${subsidiary}, user: ${userSubsidiary})`);

  // If there's a mismatch between requested and user subsidiary, log a warning
  if (userSubsidiary && subsidiary && userSubsidiary !== subsidiary && !specialCaseHandled) {
    console.warn(`Subsidiary mismatch in API request: User belongs to ${userSubsidiary}, tried to access ${subsidiary}`);
    console.warn('Using user subsidiary instead to prevent access issues');
  }

  return {
    headers: {
      "x-subsidiary": effectiveSubsidiary,
      "Content-Type": "application/json",
    },
  };
};

// Multipart header with subsidiary
const getMultipartHeaders = (subsidiary) => {
  // Get the user's subsidiary from localStorage if available
  const user = JSON.parse(localStorage.getItem('user'));
  const userSubsidiary = user?.sub;

  // Special case for premierkenya and premiergroup - they are the same entity
  let specialCaseHandled = false;
  if (userSubsidiary && subsidiary) {
    if ((userSubsidiary === 'premierkenya' && subsidiary === 'premiergroup') ||
        (userSubsidiary === 'premiergroup' && subsidiary === 'premierkenya')) {
      console.log('Special case in multipart request: premierkenya and premiergroup are treated as the same entity');
      // Use the requested subsidiary in this special case
      specialCaseHandled = true;
    }
  }

  // IMPORTANT: Always use the user's subsidiary if available
  // This ensures that users can only access their own subsidiary data
  // and prevents cross-subsidiary access issues
  const effectiveSubsidiary = specialCaseHandled ? subsidiary : (userSubsidiary || subsidiary || "platinumkenya");

  // Log the subsidiary being used for debugging
  console.log(`Multipart Request: Using subsidiary ${effectiveSubsidiary} (requested: ${subsidiary}, user: ${userSubsidiary})`);

  return {
    headers: {
      "Content-Type": "multipart/form-data",
      "x-subsidiary": effectiveSubsidiary,
    },
  };
};

// Response handler
const handleResponse = (response) => {
  // Only log in development environment
  if (process.env.NODE_ENV === 'development') {
    // Log minimal information about the response
    console.log("API Response status:", response.status, response.statusText);

    // Log structure without sensitive data
    if (response && response.data) {
      console.log("Response data structure:", {
        hasData: !!response.data,
        dataType: typeof response.data,
        isArray: Array.isArray(response.data),
        hasUsers: response.data.users ? true : false,
        hasUser: response.data.user ? true : false,
        keys: Object.keys(response.data),
        status: response.status,
        statusText: response.statusText
      });
    }
  }

  if (response && response.data) {
    return response.data;
  } else {
    console.error("No data found in response");
    throw new Error("No data found in response");
  }
};

// Dashboard
export const getDashboardSummary = async (subsidiary) => {
  const response = await api.get(
    "/dashboard/dashboard-summary",
    getSubsidiaryHeader(subsidiary)
  );
  return handleResponse(response);
};

export const getRecentActivities = async (subsidiary) => {
  const response = await api.get(
    "/dashboard/recent-activities",
    getSubsidiaryHeader(subsidiary)
  );
  return handleResponse(response);
};

export const getActivityTracker = async (subsidiary) => {
  const response = await api.get(
    "/dashboard/activity-tracker",
    getSubsidiaryHeader(subsidiary)
  );
  return handleResponse(response);
};

// Access Request Options
export const getBranches = async (subsidiary) => {
  try {
    console.log(`Fetching branches for subsidiary: ${subsidiary}`);
    const headers = getSubsidiaryHeader(subsidiary);
    console.log('Request headers:', headers);

    const response = await api.get(
      "/access-request/branches",
      headers
    );

    console.log('Branches response:', response);
    return response.data.branches || [];
  } catch (error) {
    console.error(`Error fetching branches for ${subsidiary}:`, error);
    console.error('Error details:', error.response?.data || error.message);
    // Return empty array as fallback
    return [];
  }
};

export const getRoles = async (subsidiary) => {
  try {
    console.log(`Fetching roles for subsidiary: ${subsidiary}`);
    const headers = getSubsidiaryHeader(subsidiary);
    console.log('Request headers:', headers);

    const response = await api.get(
      "/access-request/userroles",
      headers
    );

    console.log('Roles response:', response);
    return response.data.roles || [];
  } catch (error) {
    console.error(`Error fetching roles for ${subsidiary}:`, error);
    console.error('Error details:', error.response?.data || error.message);
    // Return empty array as fallback
    return [];
  }
};

export const getDepartments = async (subsidiary) => {
  try {
    console.log(`Fetching departments for subsidiary: ${subsidiary}`);
    const headers = getSubsidiaryHeader(subsidiary);
    console.log('Request headers:', headers);

    const response = await api.get(
      "/access-request/departments",
      headers
    );

    console.log('Departments response:', response);
    return response.data.dimensions || [];
  } catch (error) {
    console.error(`Error fetching departments for ${subsidiary}:`, error);
    console.error('Error details:', error.response?.data || error.message);
    // Return empty array as fallback
    return [];
  }
};

// Reports
export const getReports = async (endpoint, subsidiary) => {
  const response = await api.get(endpoint, getSubsidiaryHeader(subsidiary));
  return handleResponse(response);
};

// Audit Logs
export const getAllAuditLogs = async (subsidiary) => {
  const response = await api.get(
    "/audit-logs",
    getSubsidiaryHeader(subsidiary)
  );
  return handleResponse(response);
};

// Access
export const submitAccessForm = async (formData, subsidiary) => {
  try {
    console.log(`Submitting access form for subsidiary: ${subsidiary}`);
    const headers = getMultipartHeaders(subsidiary);
    console.log('Request headers:', headers);

    // Log form data keys (not values for privacy)
    const formDataKeys = [];
    for (let key of formData.keys()) {
      formDataKeys.push(key);
    }
    console.log('Form data keys:', formDataKeys);

    const response = await api.post("/access-request", formData, headers);
    console.log('Access form submission response:', response);
    return response;
  } catch (error) {
    console.error(`Error submitting access form for ${subsidiary}:`, error);
    console.error('Error details:', error.response?.data || error.message);
    throw error;
  }
};

export const submitRevocationForm = async (formData, subsidiary) => {
  const response = await api.post(
    "/revocation/submit",
    formData,
    getMultipartHeaders(subsidiary)
  );
  return response.data;
};

export const submitRevocationBulkUpload = async (formData, subsidiary) => {
  try {
    console.log(`Sending revocation bulk upload request for subsidiary: ${subsidiary}`);

    // Ensure the subsidiary is included in the headers
    const headers = getMultipartHeaders(subsidiary);
    console.log('Request headers:', headers);

    // Log the file being uploaded
    const file = formData.get('file');
    console.log('Uploading file:', file ? {
      name: file.name,
      type: file.type,
      size: file.size
    } : 'No file attached');

    const response = await api.post(
      "/revocation/bulk-upload",
      formData,
      headers
    );

    console.log('Bulk upload response:', response);
    return response.data;
  } catch (error) {
    console.error('Error in submitRevocationBulkUpload:', error);
    console.error('Error response:', error.response?.data);
    throw error;
  }
};

// Access Requests
export const getAllAccessRequests = async (subsidiary) => {
  try {
    console.log(`Making API call to get all access requests for subsidiary: ${subsidiary}`);
    const headers = getSubsidiaryHeader(subsidiary);
    console.log('Request headers:', headers);

    const response = await api.get(
      "/access-request",
      headers
    );

    console.log('API response received:', {
      status: response.status,
      statusText: response.statusText,
      hasData: !!response.data,
      dataLength: Array.isArray(response.data) ? response.data.length : 'not array'
    });

    return handleResponse(response);
  } catch (error) {
    console.error('Error in getAllAccessRequests:', error);
    console.error('Error response:', error.response?.data);
    console.error('Error status:', error.response?.status);
    throw error;
  }
};

export const getAccessRequestById = (id, subsidiary) =>
  api.get(`/access-request/${id}`, getSubsidiaryHeader(subsidiary));

export const updateAccessRequest = (id, data, subsidiary) =>
  api.put(`/access-request/${id}`, data, getSubsidiaryHeader(subsidiary));

export const deleteAccessRequest = (id, subsidiary) =>
  api.delete(`/access-request/${id}`, getSubsidiaryHeader(subsidiary));

export const approveHR = async (id, subsidiary) => {
  try {
    console.log(`Sending HR approval request for ID: ${id}, subsidiary: ${subsidiary}`);
    const headers = getSubsidiaryHeader(subsidiary);
    console.log('Request headers:', headers);

    const response = await api.put(
      `/access-request/${id}/approve-hr`,
      {},
      headers
    );

    return handleResponse(response);
  } catch (error) {
    console.error(`Error in approveHR for ID ${id}:`, error);
    throw error;
  }
};

export const approveExecutive = async (id, subsidiary) => {
  try {
    console.log(`Sending Executive approval request for ID: ${id}, subsidiary: ${subsidiary}`);
    const headers = getSubsidiaryHeader(subsidiary);
    console.log('Request headers:', headers);

    const response = await api.put(
      `/access-request/${id}/approve-executive`,
      {},
      headers
    );

    return handleResponse(response);
  } catch (error) {
    console.error(`Error in approveExecutive for ID ${id}:`, error);
    throw error;
  }
};

export const approveIT = async (id, subsidiary) => {
  try {
    console.log(`Sending IT approval request for ID: ${id}, subsidiary: ${subsidiary}`);
    const headers = getSubsidiaryHeader(subsidiary);
    console.log('Request headers:', headers);

    const response = await api.put(
      `/access-request/${id}/approve-it`,
      {},
      headers
    );

    return handleResponse(response);
  } catch (error) {
    console.error(`Error in approveIT for ID ${id}:`, error);
    throw error;
  }
};

export const rejectAccessRequest = async (id, subsidiary) => {
  try {
    console.log(`Sending reject request for ID: ${id}, subsidiary: ${subsidiary}`);
    const headers = getSubsidiaryHeader(subsidiary);
    console.log('Request headers:', headers);

    const response = await api.put(
      `/access-request/${id}/reject`,
      {},
      headers
    );

    return handleResponse(response);
  } catch (error) {
    console.error(`Error in rejectAccessRequest for ID ${id}:`, error);
    throw error;
  }
};

// Revocation
export const getPendingRevocations = async (subsidiary) => {
  const response = await api.get(
    "/revocation/pending",
    getSubsidiaryHeader(subsidiary)
  );
  return response.data;
};

export const getAllRevocations = async (subsidiary) => {
  const response = await api.get(
    "/revocation/audit",
    getSubsidiaryHeader(subsidiary)
  );
  return response.data;
};

export const getCompletedRevocations = async (subsidiary) => {
  const response = await api.get(
    "/revocation/completed",
    getSubsidiaryHeader(subsidiary)
  );
  return response.data;
};

export const approveRevocationHR = async (id, subsidiary) => {
  try {
    console.log(`Sending HR approval for revocation ID: ${id}, subsidiary: ${subsidiary}`);
    const headers = getSubsidiaryHeader(subsidiary);
    console.log('Request headers:', headers);

    const response = await api.put(
      `/revocation/${id}/approve-hr`,
      {},
      headers
    );

    return handleResponse(response);
  } catch (error) {
    console.error(`Error in approveRevocationHR for ID ${id}:`, error);
    throw error;
  }
};

export const approveRevocation = (id, subsidiary) =>
  api.put(`/revocation/${id}/approve`, {}, getSubsidiaryHeader(subsidiary));

export const rejectRevocation = (id, reason, subsidiary) =>
  api.put(
    `/revocation/${id}/reject`,
    { rejectionReason: reason },
    getSubsidiaryHeader(subsidiary)
  );

// User Management
export const getAllUsers = async (subsidiary) => {
  try {
    console.log('Getting all users for subsidiary:', subsidiary);
    const response = await api.get("/user", getSubsidiaryHeader(subsidiary));
    console.log('Raw getAllUsers response:', response);
    return handleResponse(response);
  } catch (error) {
    console.error('Error in getAllUsers:', error);
    throw error;
  }
};

export const getUser = async (id, subsidiary) => {
  try {
    const response = await api.get(`/user/${id}`, getSubsidiaryHeader(subsidiary));
    return handleResponse(response);
  } catch (error) {
    console.error('Error in getUser:', error);
    throw error;
  }
};

export const createUser = async (userData, subsidiary) => {
  try {
    // Only log minimal information in development environment
    if (process.env.NODE_ENV === 'development') {
      // Log user creation attempt without sensitive data
      const { password, ...safeData } = userData;
      console.log('Creating user for subsidiary:', subsidiary);
      console.log('User data fields:', Object.keys(userData));
    }

    const response = await api.post("/user", userData, getSubsidiaryHeader(subsidiary));
    return handleResponse(response);
  } catch (error) {
    console.error('Error in createUser:', error.message);
    throw error;
  }
};

export const updateUser = async (id, userData, subsidiary) => {
  try {
    // Only log minimal information in development environment
    if (process.env.NODE_ENV === 'development') {
      // Log user update attempt without sensitive data
      const { password, ...safeData } = userData;
      console.log(`Updating user ID ${id} for subsidiary:`, subsidiary);
      console.log('Fields being updated:', Object.keys(userData));
    }

    const response = await api.put(`/user/${id}`, userData, getSubsidiaryHeader(subsidiary));
    return handleResponse(response);
  } catch (error) {
    console.error('Error in updateUser:', error.message);
    throw error;
  }
};

export const deleteUser = async (id, subsidiary) => {
  try {
    const response = await api.delete(`/user/${id}`, getSubsidiaryHeader(subsidiary));
    return handleResponse(response);
  } catch (error) {
    console.error('Error in deleteUser:', error);
    throw error;
  }
};

// Authentication
export const login = (credentials, subsidiary) => {
  // Only log minimal information in development environment
  if (process.env.NODE_ENV === 'development') {
    console.log(`Login attempt for subsidiary: ${subsidiary}`);
  }

  // Create a completely fresh API instance with no shared config
  // This ensures no cached headers or state from previous requests
  const freshApi = axios.create({
    baseURL: config["BACKEND_SERVICE"],
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json"
    }
  });

  // Remove any default headers that might have been set
  delete freshApi.defaults.headers.common['Authorization'];
  delete freshApi.defaults.headers.common['x-subsidiary'];

  // Add the subsidiary to the credentials object to ensure it's in the request body
  const loginData = {
    ...credentials,
    subsidiary: subsidiary
  };

  // Create a clean headers object for this specific request
  const headers = {
    "x-subsidiary": subsidiary,
    "Content-Type": "application/json",
    "Accept": "application/json"
  };

  // Make the request with the clean headers
  return freshApi.post("/auth/login", loginData, { headers });
};

export const verifyToken = (token, subsidiary) =>
  api.post("/auth/verify-token", { token }, getSubsidiaryHeader(subsidiary));

export const getCurrentUser = (subsidiary) =>
  api.get("/auth/me", getSubsidiaryHeader(subsidiary));

export const checkPasswordChangeRequired = (subsidiary) =>
  api.get("/auth/check-password-change", getSubsidiaryHeader(subsidiary));

// Tenant Management
export const getAllTenants = async () => {
  try {
    console.log('Fetching all tenants');
    const response = await api.get("/tenant");
    console.log('Raw getAllTenants response:', response);
    return handleResponse(response);
  } catch (error) {
    console.error('Error in getAllTenants:', error);
    throw error;
  }
};

export const getTenant = async (id) => {
  try {
    const response = await api.get(`/tenant/${id}`);
    return handleResponse(response);
  } catch (error) {
    console.error('Error in getTenant:', error);
    throw error;
  }
};

export const createTenant = async (tenantData) => {
  try {
    console.log('Creating tenant with data:', tenantData);
    const response = await api.post("/tenant", tenantData);
    console.log('Raw createTenant response:', response);
    return handleResponse(response);
  } catch (error) {
    console.error('Error in createTenant:', error);
    throw error;
  }
};

export const updateTenant = async (id, tenantData) => {
  try {
    const response = await api.put(`/tenant/${id}`, tenantData);
    return handleResponse(response);
  } catch (error) {
    console.error('Error in updateTenant:', error);
    throw error;
  }
};

export const deleteTenant = async (id) => {
  try {
    const response = await api.delete(`/tenant/${id}`);
    return handleResponse(response);
  } catch (error) {
    console.error('Error in deleteTenant:', error);
    throw error;
  }
};

export default api;
