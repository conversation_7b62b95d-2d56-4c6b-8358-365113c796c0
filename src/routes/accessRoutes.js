const express = require("express");
const axios = require("axios");
const multer = require("multer");
const router = express.Router();
const upload = multer({ storage: multer.memoryStorage() });
const { uploadExcel } = require("../_middlewares/upload");

const auditTrail = require("../_middlewares/auditTrail");
const populateAuditData = require("../_middlewares/populateAuditData");
const { UserAccessRequest } = require("../db/models");
const {
  approveBranchHead,
  approveHR,
  approveExecutive,
  approveIT,
  rejectAccessRequest,
  dataMiddleware,
  generateAccessRequestReport,
  validatePhoneNumber,
} = require("../controllers/accessFormController");
const { sendEmail } = require("#src/utils/emailService");

const approverEmails = require("../config/approverEmails");

require("dotenv").config();

const SUBSIDIARY_CONFIG = {
  platinumkenya: {
    authUrl: "https://ws.platinumcredit.co.ke/ws/auth/gettoken",
    apiUrl: "https://ws.platinumcredit.co.ke/api/dimension",
    origin: "https://ws.platinumcredit.co.ke",
  },
  premierkenya: {
    authUrl: "https://cache.premiergroup.co.ke/ws/auth/gettoken",
    apiUrl: "https://cache.premiergroup.co.ke/api/dimension",
    origin: "https://cache.premiergroup.co.ke",
  },
  momentumcredit: {
    authUrl: "https://ws.momentumcredit.co.ke/ws/auth/gettoken",
    apiUrl: "https://ws.momentumcredit.co.ke/api/dimension",
    origin: "https://ws.momentumcredit.co.ke",
  },
  platinumtanzania: {
    authUrl: "https://ws.platinumcredit.co.tz/ws/auth/gettoken",
    apiUrl: "https://ws.platinumcredit.co.tz/api/dimension",
    origin: "https://ws.platinumcredit.co.tz",
  },
  premierfanikiwa: {
    authUrl: "https://ws.fmfc.co.tz/ws/auth/gettoken",
    apiUrl: "https://ws.fmfc.co.tz/api/dimension",
    origin: "https://ws.fmfc.co.tz",
  },
  spectrumzambia: {
    authUrl: "https://cache.spectrumcreditltd.com/ws/auth/gettoken",
    apiUrl: "https://cache.spectrumcreditltd.com/api/dimension",
    origin: "https://cache.spectrumcreditltd.com",
  },
  premiersouthafrica: {
    authUrl: "https://ws.premiercredit.co.za/ws/auth/gettoken",
    apiUrl: "https://ws.premiercredit.co.za/api/dimension",
    origin: "https://ws.premiercredit.co.za",
  },
  platinumuganda: {
    authUrl: "https://ws.platinumcredit.co.ug/ws/auth/gettoken",
    apiUrl: "https://ws.platinumcredit.co.ug/api/dimension",
    origin: "https://ws.platinumcredit.co.ug",
  },
  premieruganda: {
    authUrl: "https://ws.premiercredit.co.ug/ws/auth/gettoken",
    apiUrl: "https://ws.premiercredit.co.ug/api/dimension",
    origin: "https://ws.premiercredit.co.ug",
  },
};

const getSubsidiaryFromHeader = (req) => {
  const subsidiary = req.headers["x-subsidiary"]?.toLowerCase() || "platinumkenya";
  console.log(`getSubsidiaryFromHeader: Extracted subsidiary '${subsidiary}' from header '${req.headers["x-subsidiary"]}'`);
  return subsidiary;
};

const getApiToken = async (subsidiaryKey) => {
  const config = SUBSIDIARY_CONFIG[subsidiaryKey];
  if (!config) throw new Error(`Unknown subsidiary: ${subsidiaryKey}`);

  try {
    const response = await axios.post(
      config.authUrl,
      process.env.DIMENSION_API_TOKEN_PLATKE,
      {
        headers: {
          Authorization: `Bearer ${process.env.DIMENSION_API_TOKEN_PLATKE}`,
          Accept: "application/json",
          "Content-Type": "application/json",
          Origin: config.origin,
        },
      }
    );
    return {
      token: response.data,
      origin: config.origin,
      apiUrl: config.apiUrl,
    };
  } catch (error) {
    console.error(
      "Error fetching token:",
      error?.response?.data || error.message
    );
    throw new Error("Failed to fetch API token");
  }
};

const fetchDimensionData = async (req, res, endpoint, entityName) => {
  const subsidiary = getSubsidiaryFromHeader(req);
  try {
    console.log(`Fetching ${entityName} for subsidiary: ${subsidiary}`);

    // Get API token and configuration
    const { token, origin, apiUrl } = await getApiToken(subsidiary);

    if (!apiUrl) {
      console.error(`Missing apiUrl for subsidiary: ${subsidiary}`);
      return res.status(500).json({
        error: `Configuration error: Missing apiUrl for ${subsidiary}`,
        subsidiary: subsidiary
      });
    }

    console.log(`Making request to: ${apiUrl}/${endpoint}`);

    const response = await axios.get(`${apiUrl}/${endpoint}`, {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: "application/json",
        "Content-Type": "application/json",
        Origin: origin,
      },
    });

    console.log(`Successfully fetched ${entityName} for ${subsidiary}`);
    return res.json(response.data);
  } catch (error) {
    // Log detailed error information
    console.error(`Error fetching ${entityName} for ${subsidiary}:`);
    console.error(`Request URL: ${SUBSIDIARY_CONFIG[subsidiary]?.apiUrl}/${endpoint}`);
    console.error(`Error details:`, error?.response?.data || error.message);

    if (error.code === 'ECONNREFUSED') {
      console.error(`Connection refused to ${SUBSIDIARY_CONFIG[subsidiary]?.apiUrl}`);
      return res.status(500).json({
        error: `Connection refused to ${subsidiary} API server`,
        details: error.message,
        subsidiary: subsidiary
      });
    }

    return res.status(500).json({
      error: `Failed to fetch ${entityName}`,
      details: error.message,
      subsidiary: subsidiary
    });
  }
};

router.get("/branches", (req, res) =>
  fetchDimensionData(req, res, "branches", "branches")
);
router.get("/userroles", (req, res) =>
  fetchDimensionData(req, res, "roles", "roles")
);
router.get("/departments", (req, res) =>
  fetchDimensionData(req, res, "departments", "departments")
);

router.post(
  "/",
  upload.fields([
    { name: "cv" },
    { name: "id" },
    { name: "kraPin" },
    { name: "nssf" },
    { name: "sha" },
    { name: "bankDetails" },
    { name: "passportImage" },
    { name: "contract" },
    { name: "localGovId" },
    { name: "refereesLetter" },
    { name: "personalInfoForm" },
    { name: "passportSizes" },
    { name: "applicationLetter" },
    { name: "tin" },
    { name: "otherDocuments" }, // For Premier Uganda multiple file uploads
  ]),
  populateAuditData,
  auditTrail("user-request-access"),
  async (req, res) => {
    try {
      const subId = getSubsidiaryFromHeader(req);
      let { accessType } = req.body;
      if (!Array.isArray(accessType)) accessType = [accessType];

      const {
        firstName,
        middleName,
        lastName,
        nin,
        dateOfBirth,
        nextOfKinName,
        nextOfKinMobile,
        nextOfKinRelationship,
        nextOfKinDependents,
        tin,
        email,
        systemName,
        branch,
        department,
        telephone,
        role,
        previousRole,
        reason,
        staffId,
        position,
        startDate,
      } = req.body;

      // Phone number uniqueness check removed - allow duplicate phone numbers
      if (telephone) {
        console.log(`✅ Route handler - phone number validation disabled, allowing: ${telephone}`);
      }

      // Process attachments first
      const attachments = [];
      if (req.files) {
        console.log('Processing files:', Object.keys(req.files));
        for (const [key, files] of Object.entries(req.files)) {
          // Handle multiple files for otherDocuments (Premier Uganda)
          if (key === 'otherDocuments' && Array.isArray(files)) {
            files.forEach(file => {
              console.log(`Processing multiple file for key ${key}:`, file.originalname, file.mimetype);
              attachments.push({
                filename: file.originalname,
                content: file.buffer.toString("base64"),
                encoding: "base64",
                contentType: file.mimetype || "application/octet-stream",
              });
            });
          } else {
            // Handle single file for other fields
            const file = files[0];
            if (file) {
              console.log(`Processing single file for key ${key}:`, file.originalname, file.mimetype);
              attachments.push({
                filename: file.originalname,
                content: file.buffer.toString("base64"),
                encoding: "base64",
                contentType: file.mimetype || "application/octet-stream",
              });
            }
          }
        }
      }
      console.log('Total attachments processed:', attachments.length);

      // Helper function to validate and convert date
      const validateDate = (dateString) => {
        if (!dateString || dateString === '' || dateString === 'Invalid date') {
          return null;
        }
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          return null;
        }
        return date;
      };

      // Helper function to handle optional string fields
      const validateOptionalString = (value) => {
        return (value && value.trim() !== '') ? value.trim() : null;
      };

      // Helper function to handle email field (required in database)
      const validateRequiredEmail = (value) => {
        if (value && value.trim() !== '') {
          return value.trim();
        }
        // Return a placeholder email if none provided (to satisfy database constraint)
        return '<EMAIL>';
      };

      // Base request data that applies to all subsidiaries
      const requestData = {
        firstName,
        lastName,
        email: validateRequiredEmail(email),
        systemName,
        branch,
        department,
        telephone,
        accessType,
        role,
        previousRole: validateOptionalString(previousRole),
        reason,
        subId,
        attachments, // Include attachments in the database record
      };

      // Add Premier Uganda specific fields only for premieruganda subsidiary
      if (subId === 'premieruganda') {
        requestData.middleName = validateOptionalString(middleName);
        requestData.nin = validateOptionalString(nin);
        requestData.dateOfBirth = validateDate(dateOfBirth);
        requestData.nextOfKinName = nextOfKinName;
        requestData.nextOfKinMobile = nextOfKinMobile;
        requestData.nextOfKinRelationship = nextOfKinRelationship;
        requestData.nextOfKinDependents = nextOfKinDependents;
        requestData.tin = validateOptionalString(tin);
        requestData.staffId = validateOptionalString(staffId);
        requestData.position = validateOptionalString(position);
        requestData.startDate = validateDate(startDate);
      }

      const request = await UserAccessRequest.create(requestData);

      const approvers = approverEmails[subId];

      if (!approvers) {
        return res.status(400).json({ error: "Invalid subsidiary" });
      }

      sendEmail({
        to: approvers.HR,
        subject: "New Access Request Pending Approval",
        tenant: subId,
        emailBody: `
          <p>Dear HR,</p>
          <p>A new access request has been submitted and is awaiting your approval.</p>
          <table style="border-collapse: collapse; width: auto;">
            <tr><td style="font-weight: bold;">Requestor:</td><td>${firstName} ${lastName}</td></tr>
            <tr><td style="font-weight: bold;">Email:</td><td><a href="mailto:${email}">${email}</a></td></tr>
            <tr><td style="font-weight: bold;">System Name:</td><td>${systemName}</td></tr>
            <tr><td style="font-weight: bold;">Branch:</td><td>${branch}</td></tr>
            <tr><td style="font-weight: bold;">Department:</td><td>${department}</td></tr>
            <tr><td style="font-weight: bold;">Access Type:</td><td>${accessType.join(", ")}</td></tr>
            <tr><td style="font-weight: bold;">Role:</td><td>${role}</td></tr>
            <tr><td style="font-weight: bold;">Reason:</td><td>${reason || "N/A"}</td></tr>
          </table>
          <p><a href="https://uat-uap.platcorpgroup.com/user-access/ui/approval/${subId}" target="_blank">Click here to review and approve this request</a></p>
        `,
        tenant: subId,
        attachments, // Include attachments in the email
      });

      return res.status(201).json(request);
    } catch (error) {
      console.error("Error Creating Access Request:", error);
      return res.status(500).json({
        error: "Internal Server Error",
        details: error.message,
      });
    }
  }
);

// Bulk upload endpoint for Premier Uganda
router.post("/bulk-upload/:subsidiary", uploadExcel, async (req, res) => {
  try {
    const subsidiary = req.params.subsidiary;

    if (subsidiary !== 'premieruganda') {
      return res.status(400).json({
        error: "Bulk upload is only available for Premier Uganda"
      });
    }

    if (!req.file) {
      return res.status(400).json({
        error: "No file uploaded"
      });
    }

    const XLSX = require('xlsx');
    const workbook = XLSX.readFile(req.file.path);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNumber = i + 2; // Excel row number (accounting for header)

      try {
        // Validate required fields
        const requiredFields = [
          'FirstName', 'LastName', 'NIN', 'DateOfBirth', 'MobilePhone',
          'NextOfKinName', 'NextOfKinMobile', 'NextOfKinRelationship',
          'NextOfKinDependents', 'Branch', 'SystemName', 'AccessType', 'Reason'
        ];

        const missingFields = requiredFields.filter(field => !row[field]);
        if (missingFields.length > 0) {
          throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Validate NIN format (14 characters)
        if (row.NIN && row.NIN.length !== 14) {
          throw new Error(`NIN must be exactly 14 characters, got ${row.NIN.length}`);
        }

        // Helper function to validate and convert date
        const validateDate = (dateString) => {
          if (!dateString || dateString === '' || dateString === 'Invalid date') {
            return null;
          }
          const date = new Date(dateString);
          if (isNaN(date.getTime())) {
            return null;
          }
          return date;
        };

        // Helper function to handle optional string fields
        const validateOptionalString = (value) => {
          return (value && value.trim() !== '') ? value.trim() : null;
        };

        // Helper function to handle email field (required in database)
        const validateRequiredEmail = (value) => {
          if (value && value.trim() !== '') {
            return value.trim();
          }
          // Return a placeholder email if none provided (to satisfy database constraint)
          return '<EMAIL>';
        };

        // Create request data object
        const requestData = {
          firstName: row.FirstName,
          lastName: row.LastName,
          email: validateRequiredEmail(row.Email),
          telephone: row.MobilePhone,
          branch: row.Branch,
          systemName: row.SystemName,
          department: '', // Will be filled by HR in approval flow
          accessType: row.AccessType,
          role: '', // Will be filled by HR in approval flow
          reason: row.Reason,
          approvalStatus: "Pending",
          subId: subsidiary,
          attachments: []
        };

        // Add Premier Uganda specific fields
        if (subsidiary === 'premieruganda') {
          requestData.middleName = validateOptionalString(row.MiddleName);
          requestData.nin = validateOptionalString(row.NIN);
          requestData.dateOfBirth = validateDate(row.DateOfBirth);
          requestData.nextOfKinName = row.NextOfKinName;
          requestData.nextOfKinMobile = row.NextOfKinMobile;
          requestData.nextOfKinRelationship = row.NextOfKinRelationship;
          requestData.nextOfKinDependents = row.NextOfKinDependents;
          requestData.tin = validateOptionalString(row.TIN);
        }

        // Create access request
        const accessRequest = await UserAccessRequest.create(requestData);

        results.push({
          row: rowNumber,
          success: true,
          message: `Access request created successfully for ${row.FirstName} ${row.LastName}`
        });
        successCount++;

      } catch (error) {
        console.error(`Error processing row ${rowNumber}:`, error);
        results.push({
          row: rowNumber,
          success: false,
          message: error.message
        });
        errorCount++;
      }
    }

    // Clean up uploaded file
    const fs = require('fs');
    if (fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.json({
      message: `Bulk upload completed. ${successCount} successful, ${errorCount} errors.`,
      successCount,
      errorCount,
      results
    });

  } catch (error) {
    console.error("Bulk upload error:", error);
    res.status(500).json({
      error: "Bulk upload failed",
      details: error.message
    });
  }
});

// Report endpoint for auditors and IT personnel
router.get("/report", generateAccessRequestReport);

// Phone number validation endpoint
router.get("/validate-phone", validatePhoneNumber);

// Test endpoint for debugging
router.get("/test", async (req, res) => {
  try {
    const subId = getSubsidiaryFromHeader(req);
    console.log(`Test endpoint called for subsidiary: ${subId}`);
    console.log(`Request headers:`, req.headers);
    console.log(`User:`, req.user ? { id: req.user.id, role: req.user.role, sub: req.user.sub } : 'No user');

    res.json({
      message: "Test endpoint working",
      subsidiary: subId,
      timestamp: new Date().toISOString(),
      hasUser: !!req.user,
      userRole: req.user?.role,
      userSub: req.user?.sub
    });
  } catch (error) {
    console.error("Test endpoint error:", error);
    res.status(500).json({ error: error.message });
  }
});

// Update HR fields for Premier Uganda
router.put("/:id/hr-fields", async (req, res) => {
  try {
    const requestId = req.params.id;
    const { staffId, position, department, startDate } = req.body;
    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase();

    if (subsidiary !== 'premieruganda') {
      return res.status(400).json({
        error: "HR fields update is only available for Premier Uganda"
      });
    }

    // Validate required fields
    if (!staffId || !position || !department || !startDate) {
      return res.status(400).json({
        error: "All HR fields are required: staffId, position, department, startDate"
      });
    }

    // Update the access request with HR fields
    const [updatedRows] = await UserAccessRequest.update({
      staffId,
      position,
      department,
      startDate
    }, {
      where: { id: requestId, subId: subsidiary }
    });

    if (updatedRows === 0) {
      return res.status(404).json({
        error: "Access request not found or not authorized"
      });
    }

    res.json({
      message: "HR fields updated successfully",
      data: { staffId, position, department, startDate }
    });

  } catch (error) {
    console.error("Error updating HR fields:", error);
    res.status(500).json({
      error: "Failed to update HR fields",
      details: error.message
    });
  }
});

router.get("/", async (req, res) => {
  try {
    const subId = getSubsidiaryFromHeader(req);
    console.log(`Fetching access requests for subsidiary: ${subId}`);
    console.log(`Request headers:`, req.headers);

    // For premierfanikiwa subsidiary, we need to exclude middleName field
    // since the migration hasn't been applied yet
    let requests;
    if (subId === 'premierfanikiwa') {
      console.log('Using premierfanikiwa specific query (excluding Premier Uganda fields)');
      // Get all columns except middleName and other Premier Uganda specific fields
      requests = await UserAccessRequest.findAll({
        where: { subId },
        order: [["createdAt", "DESC"]],
        attributes: [
          'id', 'subId', 'systemName', 'branch', 'firstName', 'lastName',
          'email', 'telephone', 'department', 'accessType', 'role',
          'previousRole', 'reason', 'attachments', 'approvalStatus',
          'createdAt', 'updatedAt'
        ]
      });
    } else {
      console.log('Using standard query for subsidiary:', subId);
      // For other subsidiaries, get all fields
      requests = await UserAccessRequest.findAll({
        where: { subId },
        order: [["createdAt", "DESC"]]
      });
    }

    console.log(`Successfully fetched ${requests.length} requests for subsidiary: ${subId}`);
    return res.json(requests);
  } catch (error) {
    console.error("Error Fetching Requests:", error);
    console.error("Error details:", error.message);
    console.error("Error stack:", error.stack);

    // Check if it's a database connection error
    if (error.name === 'SequelizeConnectionError') {
      return res.status(500).json({
        error: "Database connection error",
        details: "Could not connect to the database. Please check your database connection."
      });
    }

    // Check if it's a database query error
    if (error.name === 'SequelizeDatabaseError') {
      return res.status(500).json({
        error: "Database query error",
        details: error.message
      });
    }

    return res.status(500).json({
      error: "Internal Server Error",
      details: error.message
    });
  }
});

router.get("/:id", async (req, res) => {
  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) return res.status(404).json({ error: "Request Not Found" });

    // For premierfanikiwa subsidiary, filter out Premier Uganda specific fields
    // since the migration hasn't been applied yet
    if (request.subId === 'premierfanikiwa') {
      const filteredRequest = {
        id: request.id,
        subId: request.subId,
        systemName: request.systemName,
        branch: request.branch,
        firstName: request.firstName,
        lastName: request.lastName,
        email: request.email,
        telephone: request.telephone,
        department: request.department,
        accessType: request.accessType,
        role: request.role,
        previousRole: request.previousRole,
        reason: request.reason,
        attachments: request.attachments,
        approvalStatus: request.approvalStatus,
        createdAt: request.createdAt,
        updatedAt: request.updatedAt
      };
      return res.json(filteredRequest);
    } else {
      return res.json(request);
    }
  } catch (error) {
    console.error("Error Fetching Request:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
});

router.put("/:id", async (req, res) => {
  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) return res.status(404).json({ error: "Request Not Found" });
    await request.update(req.body);
    return res.json(request);
  } catch (error) {
    console.error("Error Updating Request:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
});

router.delete("/:id", async (req, res) => {
  try {
    const request = await UserAccessRequest.findByPk(req.params.id);
    if (!request) return res.status(404).json({ error: "Request Not Found" });
    await request.destroy();
    return res.json({ message: "Request Deleted Successfully" });
  } catch (error) {
    console.error("Error Deleting Request:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
});

router.put(
  "/:id/approve-branch-head",
  dataMiddleware,
  auditTrail("branch-head-approval"),
  approveBranchHead
);
router.put(
  "/:id/approve-hr",
  dataMiddleware,
  auditTrail("hr-approval"),
  approveHR
);
router.put(
  "/:id/approve-executive",
  dataMiddleware,
  auditTrail("executive-approval"),
  approveExecutive
);
router.put(
  "/:id/approve-it",
  dataMiddleware,
  auditTrail("IT-approval"),
  approveIT
);
router.put("/:id/reject", rejectAccessRequest, auditTrail("rejection"));

module.exports = router;
