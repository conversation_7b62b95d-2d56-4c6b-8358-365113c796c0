'use strict';
const bcrypt = require('bcryptjs');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Get the premieruganda tenant ID
    const tenants = await queryInterface.sequelize.query(
      `SELECT id FROM tenant WHERE sub = 'premieruganda'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (tenants.length === 0) {
      console.log('Premier Uganda tenant not found. Please run the tenant seeder first.');
      return;
    }

    const tenantId = tenants[0].id;
    // Hash passwords
    const salt = bcrypt.genSaltSync(10);
    const hashPassword = (password) => bcrypt.hashSync(password, salt);
    const users = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: hashPassword('Admin@123'),
        sub: 'premieruganda',
        subId: tenantId.toString(),
        employerId: 'EMP001',
        role: 'systemAdmin',
        status: 'active',
        requirepasswordchange: true, // Require password change on first login
        createdat: new Date(),
        updatedat: new Date()
      },
      {
        username: 'hr',
        email: '<EMAIL>',
        password: hashPassword('Hr@123'),
        sub: 'premieruganda',
        subId: tenantId.toString(),
        employerId: 'EMP002',
        role: 'hr',
        status: 'active',
        requirepasswordchange: true, // Require password change on first login
        createdat: new Date(),
        updatedat: new Date()
      },
      {
        username: 'it',
        email: '<EMAIL>',
        password: hashPassword('It@123'),
        sub: 'premieruganda',
        subId: tenantId.toString(),
        employerId: 'EMP003',
        role: 'it',
        status: 'active',
        requirepasswordchange: true, // Require password change on first login
        createdat: new Date(),
        updatedat: new Date()
      },
      {
        username: 'supervisor',
        email: '<EMAIL>',
        password: hashPassword('Supervisor@123'),
        sub: 'premieruganda',
        subId: tenantId.toString(),
        employerId: 'EMP004',
        role: 'supervisor',
        status: 'active',
        requirepasswordchange: true, // Require password change on first login
        createdat: new Date(),
        updatedat: new Date()
      }
    ];

    // Check for existing users to avoid duplicates
    for (const user of users) {
      const existingUser = await queryInterface.sequelize.query(
        `SELECT * FROM "user" WHERE email = '${user.email}'`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (existingUser.length === 0) {
        await queryInterface.bulkInsert('user', [user]);
        console.log(`User ${user.username} created successfully for Premier Uganda`);
      } else {
        console.log(`User ${user.username} already exists for Premier Uganda, skipping creation`);
      }
    }
  },

  async down(queryInterface, Sequelize) {
    // Get the premieruganda tenant ID
    const tenants = await queryInterface.sequelize.query(
      `SELECT id FROM tenant WHERE sub = 'premieruganda'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (tenants.length === 0) {
      return;
    }

    const tenantId = tenants[0].id;

    // Delete the users for premieruganda
    await queryInterface.bulkDelete('user', {
      subId: tenantId.toString()
    });
  }
};
