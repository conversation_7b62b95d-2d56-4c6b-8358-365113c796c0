"use strict";
const { Model, DataTypes, Sequelize } = require("sequelize");
module.exports = (sequelize) => {
  class UserAccessRequest extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define association here if needed
    }
  }

  UserAccessRequest.init(
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      subId: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      systemName: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      branch: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      firstName: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      middleName: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      lastName: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      nin: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      dateOfBirth: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      nextOfKinName: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      nextOfKinMobile: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      nextOfKinRelationship: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      nextOfKinDependents: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      tin: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: true, // Allow null for Premier Uganda subsidiary
        validate: {
          isEmail: {
            msg: 'Must be a valid email address'
          },
          // Custom validation to check email requirement based on subsidiary
          customEmailValidation(value) {
            // If email is provided, it must be valid (handled by isEmail above)
            // If email is null/empty, check if it's allowed for this subsidiary
            if (!value || value.trim() === '') {
              // For Premier Uganda, email is optional
              if (this.subId === 'premieruganda') {
                return; // Allow null/empty email
              }
              // For all other subsidiaries, email is required
              throw new Error('Email is required for this subsidiary');
            }
          }
        },
      },
      telephone: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      department: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      accessType: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: false,
      },
      role: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      previousRole: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      staffId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      position: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      startDate: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      attachments: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      approvalStatus: {
        type: DataTypes.STRING,
        defaultValue: "Pending",
      },
    },
    {
      sequelize,
      modelName: "UserAccessRequest",
      tableName: "UserAccessRequests",
      timestamps: true,
    }
  );

  return UserAccessRequest;
};
