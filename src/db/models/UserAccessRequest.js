"use strict";
const { Model, DataTypes, Sequelize } = require("sequelize");
module.exports = (sequelize) => {
  class UserAccessRequest extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define association here if needed
    }
  }

  UserAccessRequest.init(
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      subId: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      systemName: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      branch: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      firstName: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      middleName: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      lastName: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      nin: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      dateOfBirth: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      nextOfKinName: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      nextOfKinMobile: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      nextOfKinRelationship: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      nextOfKinDependents: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      tin: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          isEmail: true,
        },
      },
      telephone: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      department: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      accessType: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: false,
      },
      role: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      previousRole: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      staffId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      position: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      startDate: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      attachments: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      approvalStatus: {
        type: DataTypes.STRING,
        defaultValue: "Pending",
      },
    },
    {
      sequelize,
      modelName: "UserAccessRequest",
      tableName: "UserAccessRequests",
      timestamps: true,
    }
  );

  return UserAccessRequest;
};
